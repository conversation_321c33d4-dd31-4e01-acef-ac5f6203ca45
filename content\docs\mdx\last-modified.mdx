---
title: Last Modified Time
description: Output the last modified time of a document
---

## Usage

This feature is not enabled by default, you can enable this from the config file. Note that it only supports Git as version control.
Please ensure you have Git installed on your machine, and **the repository is not shallow cloned**, as it relies on your local Git history.

```ts title="source.config.ts"
import { defineConfig } from 'fumadocs-mdx/config';

export default defineConfig({
  lastModifiedTime: 'git', // [!code highlight]
});
```

### Access the Property

After doing this, a `lastModified` number will be exported for each document. You can convert it to a JavaScript Date object.

```ts
import { source } from '@/lib/source';

const page = source.getPage(['...']);

console.log(new Date(page.data.lastModified));
// or with async mode:
const { lastModified } = await page.data.load();
console.log(new Date(lastModified));
```
