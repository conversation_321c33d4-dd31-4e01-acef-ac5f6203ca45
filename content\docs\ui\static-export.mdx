---
title: Static Export
description: Enable static export with Fumadocs
---

## Overview

Fumadocs is fully compatible with Next.js static export, allowing you to export the app as a static HTML site without a Node.js server.

```js title="next.config.mjs"
/**
 * @type {import('next').NextConfig}
 */
const nextConfig = {
  output: 'export',

  // Optional: Change links `/me` -> `/me/` and emit `/me.html` -> `/me/index.html`
  // trailingSlash: true,

  // Optional: Prevent automatic `/me` -> `/me/`, instead preserve `href`
  // skipTrailingSlashRedirect: true,
};
```

See [Next.js docs](https://nextjs.org/docs/app/guides/static-exports) for limitations and details.

## Search

### Built-in Search

You will need extra configurations to statically store the search indexes, and search will be computed on browser instead:

1. **Search Client:** [enable static mode](/docs/ui/search/orama#static).
2. **Search Server:** [output static indexes](/docs/headless/search/orama#static-export).

### Cloud Solutions

Since the search functionality is powered by remote servers, static export works without configuration.
