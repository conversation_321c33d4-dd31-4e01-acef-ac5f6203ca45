---
title: Why do you need docs?
description: You've read so many docs, but are they necessary?
date: 2024-05-26
author: <PERSON><PERSON>
---

People often ask me, do we really need a framework to build docs sites? Well, **You don't**.

Documentation sites are so important in software development,
internal docs for developers in your company to understand the architecture,
docs for frameworks,
docs for web standards...

Building docs seems simple, but can be difficult.

There are so many paradigms to build docs, but writing beginner-friendly docs could be difficult.
As a result, people tend to use powerful docs frameworks, making the docs site interactive and straightforward.

## Over-Engineered

For the docs of a small library/API service, you probably don't need to setup a Next.js site and spend time writing your site.
Neither Nextra nor Fumadocs could be better than GitHub wiki and Swagger docs in this case.

They offer a good, decent UI, basic functionalities, and a proper workflow of authoring docs.
The DX is good enough, I can't think of a reason to switch to a full-powered docs framework just to make your docs look fancy.

I'll just recommend writing your docs in markdown, make it accessible on your GitHub repository.

## Why Framework?

Now you may wonder, why major services and frameworks have their own docs sites built with docs frameworks?

Of course, _Usually_ using things like GitHub Wiki is adequate, but it is not always true.
Let's take Component Library for example, you cannot showcase your components with Markdown.
You will constantly find an ordinary Markdown document lacks capability and flexibility.

Documentation frameworks aim to solve this problem, with the ability to integrate with major libraries like **React.js** and **Vue.js**.
Good examples are Vitepress, Mintlify and Nextra - They made writing docs more convenient and effective, while offering a better, dedicated experience to readers.

For anything more than a simple library or API service, **it is worth trying.**

### Reinvent the Wheels

I would never recommend building a "custom docs site" on your own, without a proper docs framework.
Despite the **Don't re-invent the wheels** principle, your hand-made docs site actually takes way more effort to make it decent.

1. Document Search
2. A user-friendly navigation experience
3. Reading experience
4. UI/UX Design

Implementing them properly already sounds nerve-racking, right?

The docs itself, is definitely not your first priority. You should never spend your precious time re-inventing the wheels - **it isn't worth it**.

From my perspective, I'd rather use GitHub Wiki than re-inventing the wheels.
Why don't pick a decent solution? It saves your indispensable time, and help reduce the shitty docs sites on the internet.

## What do we focus at Fumadocs?

I personally value reading experience more than a fancy eye-catching UI.
You may notice, we do not have animations everywhere, and we avoided many fancy designs.

Fanciness of UI should stay only in landing page, a docs site should focus on **content.**
Navigation elements are helpers to browse your site, never should they take up too much space on the screen.

One thing I hated the most is the design of _two sidebars_, it is confusing and meaningless.
You can just organize all items to a single, clean sidebar, but people instead added two hamburger buttons to the navbar.

<div className='mx-auto max-w-[400px]'>

![Next.js Docs](/blog/img.png)

</div>

My favourite docs site is still [Linear docs](https://linear.app/docs), looks good and simple.

## Conclusion

1. You don't need a full-powered docs framework for a small library
2. Don't make a docs site on your own, use a proper docs framework
3. Fumadocs focuses on reading experience
4. You should focus on content, too
