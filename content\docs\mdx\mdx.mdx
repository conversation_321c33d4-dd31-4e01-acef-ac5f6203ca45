---
title: Default MDX Config
description: Customise the default configurations for MDX processor.
---

## Extended MDX Options [#extended]

Fumadocs MDX will apply some default MDX options, to make features like **syntax highlighting** work out of the box.

To allow overriding the defaults, Fumadocs MDX supports **Extended MDX Options** on top of [`ProcessorOptions`](https://mdxjs.com/packages/mdx/#processoroptions).
Additional options below:

### Remark Plugins

These plugins are applied by default:

- [Remark Image](/docs/headless/mdx/remark-image) - Handle images
- [Remark Heading](/docs/headless/mdx/headings) - Extract table of contents
- [Remark Structure](/docs/headless/mdx/structure) - Generate search indexes
- Remark Exports - Exports the output generated by remark plugins above

Add other remark plugins with:

```ts tab="Global Config"
import { defineConfig } from 'fumadocs-mdx/config';
import { myPlugin } from './remark-plugin';

export default defineConfig({
  mdxOptions: {
    remarkPlugins: [myPlugin],
    // You can also pass a function to control the order of remark plugins.
    remarkPlugins: (v) => [myPlugin, ...v],
  },
});
```

```ts tab="Collection Config"
import { defineCollections, getDefaultMDXOptions } from 'fumadocs-mdx/config';
import { myPlugin } from './remark-plugin';

export const blog = defineCollections({
  type: 'doc',
  mdxOptions: getDefaultMDXOptions({
    remarkPlugins: [myPlugin],
    // You can also pass a function to control the order of remark plugins.
    remarkPlugins: (v) => [myPlugin, ...v],
  }),
});
```

### Rehype Plugins

These plugins are applied by default:

- [Rehype Code](/docs/headless/mdx/rehype-code) - Syntax highlighting

Same as remark plugins, you can pass an array or a function to add other rehype plugins.

```ts tab="Global Config"
import { defineConfig } from 'fumadocs-mdx/config';
import { myPlugin } from './rehype-plugin';

export default defineConfig({
  mdxOptions: {
    rehypePlugins: (v) => [myPlugin, ...v],
  },
});
```

```ts tab="Collection Config"
import { defineCollections, getDefaultMDXOptions } from 'fumadocs-mdx/config';
import { myPlugin } from './rehype-plugin';

export const blog = defineCollections({
  type: 'doc',
  mdxOptions: getDefaultMDXOptions({
    rehypePlugins: (v) => [myPlugin, ...v],
  }),
});
```

### Customise Built-in Plugins

Customise the options of built-in plugins like:

```ts tab="Global Config"
import { defineConfig } from 'fumadocs-mdx/config';

export default defineConfig({
  mdxOptions: {
    rehypeCodeOptions: {
      // options
    },
    remarkImageOptions: {
      // options
    },
    remarkHeadingOptions: {
      // options
    },
  },
});
```

```ts tab="Collection Config"
import { defineCollections, getDefaultMDXOptions } from 'fumadocs-mdx/config';

export const blog = defineCollections({
  type: 'doc',
  mdxOptions: getDefaultMDXOptions({
    rehypeCodeOptions: {
      // options
    },
    remarkImageOptions: {
      // options
    },
    remarkHeadingOptions: {
      // options
    },
  }),
});
```

### Export Properties from `vfile.data`

Some remark plugins store their output in `vfile.data` (compile-time memory) which cannot be accessed from your code.
Fumadocs MDX applies a remark plugin that turns `vfile.data` properties into ESM exports, so you can access these properties when importing the MDX file.

You can define additional properties to be exported.

```ts tab="Global Config"
import { defineConfig } from 'fumadocs-mdx/config';

export default defineConfig({
  mdxOptions: {
    valueToExport: ['dataName'],
  },
});
```

```ts tab="Collection Config"
import { defineCollections, getDefaultMDXOptions } from 'fumadocs-mdx/config';

export const blog = defineCollections({
  type: 'doc',
  mdxOptions: getDefaultMDXOptions({
    valueToExport: ['dataName'],
  }),
});
```

By default, it includes:

- `toc` for the Remark Heading plugin
- `structuredData` for the Remark Structure Plugin
- `frontmatter` for the frontmatter of MDX (using `gray-matter`)
