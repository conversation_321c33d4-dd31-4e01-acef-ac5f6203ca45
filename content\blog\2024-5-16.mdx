---
title: 500 Stars
description: The first 500 stars of Fumadocs
author: <PERSON><PERSON>
date: 2024-05-16
---

It was surprising when I first saw [the video from Web Dev Cody](https://youtu.be/7HUmDAgXI2E?si=CR9fC-f3nysPJJCE), it felt like I had finally built something worth mentioning, instead of another neglected side project that nobody cares about.

So far I've worked on this project for about 1 year, and it is a precious experience to me. The best lecture that you can't find in a university.

## Open Source

I received much feedback, issues and questions. Some are brutal but straightforward, like _"please fix it, help me please"_ _"Support XXX please"_. Some are kind and helpful, willing to provide a PR.
I felt both the good and bad sides of open source.

Sometimes, people are being eager and impulsive because of stress and may spread their frustration on the library maintainers.
Even myself, can be affected by stress or a bad mood.

People may hope for instant answers, and maintainers will face questions like _"Why are you running away from my questions?"_.
I understand a library performing bad can cause your precious time to be wasted, but even if we put all our efforts in maintaining the library, it can't be flawless.

Since then, I feel I'm way more respectful to open-source project maintainers.
At least, I'll check my before before firing an issue on somebody else's Github repository, and try to be as respectful as I can ~~with my poor language skills~~.

### Issues

My favourite dev was [<PERSON> Shew](https://shew.dev), I mentioned him because [the issue he opened](https://github.com/fuma-nama/fumadocs/issues/264) is the best I've ever seen in my open-source career.
He actually cared about my vision and opinion about the API design, and gave a really concise and constructive feature request.

Obviously, I was not a perfect, neither an experienced library dev.
Fumadocs wasn't capable of many things, such a well-explained feature request and idea is powerful. His passion is inspiring.

Before setting up the YAML issue template, there were very few issues that actually followed the issue template, most of them don't even provide a reproducible repository, or an explanation.

**Open a proper issue, follow the instructions, and give maintainers some positive feedback.** This is the biggest motivation you can give to maintainers without specnding money.

## Docs

It's fun to work on the docs of fumadocs. When first building it, I had very little experience on authoring documentation and tutorial content.
Reading through the feedback from developers, the most common problem is that they can't find the docs of something.

I realized the entire docs is unfriendly for beginners to start with, my friends are senior Next.js devs, and of course they are fine with that.
However, not every dev can understand the docs.

So recently, I started to re-write some of the sections, making it easier and appealing for beginners. Welcome to share your feedback on Github Discussions!

## What is Next?

Web Development is an ever-evolving industry, but I believe the spirit of open source won't disappear.
I don't know what I will be building in the future, and that doesn't matter.
Let's build a better web!
