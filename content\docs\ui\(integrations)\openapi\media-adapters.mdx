---
title: Media Adapters
description: Support other media types
---

## Overview

A media adapter in Fumadocs supports:

- Converting value into `fetch()` body compatible with corresponding media type.
- Generate code example based on different programming language/tool.

Put your media adapters in a separate file.

```ts tab="lib/media-adapters.ts" twoslash
import type { MediaAdapter } from 'fumadocs-openapi';

export const myAdapter: MediaAdapter = {
  encode(data) {
    return JSON.stringify(data.body);
  },
  // returns code that inits a `body` variable, used for request body
  generateExample(data, ctx) {
    if (ctx.lang === 'js') {
      return `const body = "hello world"`;
    }

    if (ctx.lang === 'python') {
      return `body = "hello world"`;
    }

    if (ctx.lang === 'go' && 'addImport' in ctx) {
      ctx.addImport('strings');

      return `body := strings.NewReader("hello world")`;
    }
  },
};
```

```ts tab="lib/media-adapters.client.ts"
'use client';

// forward them so that Fumadocs can also use your media adapter in a client component
export { myAdapter } from './media-adapters';
```

Pass the adapter.

```ts title="lib/source.ts"
import { createOpenAPI } from 'fumadocs-openapi/server';
import * as Adapters from './media-adapters';
import * as ClientAdapters from './media-adapters.client';

export const openapi = createOpenAPI({
  proxyUrl: '/api/proxy',
  mediaAdapters: {
    // [!code ++:4] override the default adapter of `application/json`
    'application/json': {
      ...Adapters.myAdapter,
      client: ClientAdapters.myAdapter,
    },
  },
});
```
