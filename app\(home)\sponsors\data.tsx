/**
 * Users who sponsored with their personal account as an organization
 */
export const organizationAsUserSponsors = [
  {
    url: 'https://mintlify.com',
    asUser: 'handotdev',
    label: 'Mintlify',
    github: 'mintlify',
    logo: (
      <svg
        viewBox="0 0 103 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-auto h-8"
      >
        <path
          d="M33.8891 18H30.7691V4H34.5891L38.4091 14.26L42.2091 4H46.0291V18H42.9091V9.36L39.6891 18H37.1091L33.8891 9.36V18ZM49.5854 6.34C49.0787 6.34 48.6454 6.16667 48.2854 5.82C47.9387 5.46 47.7654 5.02 47.7654 4.5C47.7654 3.99333 47.9387 3.56667 48.2854 3.22C48.6454 2.87333 49.0787 2.7 49.5854 2.7C50.1054 2.7 50.5387 2.87333 50.8854 3.22C51.2321 3.56667 51.4054 3.99333 51.4054 4.5C51.4054 5.02 51.2321 5.46 50.8854 5.82C50.5387 6.16667 50.1054 6.34 49.5854 6.34ZM51.1254 18H48.0454V7.4H51.1254V18ZM52.9626 18V7.4H55.4426L55.7026 8.9C55.9959 8.34 56.4492 7.88667 57.0626 7.54C57.6892 7.19333 58.3892 7.02 59.1626 7.02C60.2826 7.02 61.1759 7.38 61.8426 8.1C62.5226 8.82 62.8626 9.78667 62.8626 11V18H59.7826V11.16C59.7826 10.7067 59.6359 10.3467 59.3426 10.08C59.0492 9.81333 58.6492 9.68 58.1426 9.68C57.5426 9.68 57.0426 9.86 56.6426 10.22C56.2426 10.58 56.0426 11.0467 56.0426 11.62V18H52.9626ZM69.2688 18C68.0022 18 67.0288 17.7067 66.3488 17.12C65.6822 16.5333 65.3488 15.68 65.3488 14.56V10H63.6088V7.4H65.3488V3.78H68.4288V7.4H71.0288V10H68.4288V14.22C68.4288 14.6067 68.5288 14.9 68.7288 15.1C68.9288 15.3 69.2155 15.4 69.5888 15.4H70.8088V18H69.2688ZM75.3891 18H72.3091V3H75.3891V18ZM78.7854 6.34C78.2787 6.34 77.8454 6.16667 77.4854 5.82C77.1387 5.46 76.9654 5.02 76.9654 4.5C76.9654 3.99333 77.1387 3.56667 77.4854 3.22C77.8454 2.87333 78.2787 2.7 78.7854 2.7C79.3054 2.7 79.7387 2.87333 80.0854 3.22C80.4321 3.56667 80.6054 3.99333 80.6054 4.5C80.6054 5.02 80.4321 5.46 80.0854 5.82C79.7387 6.16667 79.3054 6.34 78.7854 6.34ZM80.3254 18H77.2454V7.4H80.3254V18ZM83.3426 18V10H81.6026V7.4H83.3426V6.24C83.3426 5.18667 83.6626 4.38667 84.3026 3.84C84.9426 3.28 85.8692 3 87.0826 3H89.3426V5.6H87.4026C86.7492 5.6 86.4226 5.92667 86.4226 6.58V7.4H89.1426V10H86.4226V18H83.3426ZM90.4591 22V19.4H92.5991C93.3191 19.4 93.7725 19.1467 93.9591 18.64L94.1991 18H93.7391L89.5391 7.4H92.8391L95.4791 14.54L98.1191 7.4H101.319L96.8391 19.42C96.5191 20.2867 96.0191 20.9333 95.3391 21.36C94.6591 21.7867 93.7458 22 92.5991 22H90.4591Z"
          fill="currentColor"
        />
        <g clipPath="url(#clip0_115_7772)">
          <path
            d="M5.53338 7.66241C5.54806 5.66259 6.34114 3.74714 7.74443 2.32227H7.74185L2.32412 7.73999H2.32671C2.30654 7.75586 2.28753 7.77314 2.26981 7.79171C0.95223 9.10757 0.153396 10.8545 0.0199146 12.7118C-0.113567 14.5691 0.42726 16.4123 1.54314 17.9031L7.01517 12.431L7.07206 12.3767C6.05218 11.0186 5.51109 9.36073 5.53338 7.66241Z"
            fill="#0C8C5E"
          />
          <path
            d="M18.6882 13.2688C17.6513 14.285 16.3497 14.9894 14.9318 15.3017C13.514 15.614 12.0369 15.5216 10.669 15.0351C9.93963 14.7761 9.25417 14.4073 8.63634 13.9412L8.57945 13.9981L3.10742 19.4675C4.59878 20.5806 6.44095 21.1197 8.29711 20.9863C10.1533 20.8529 11.8994 20.0558 13.2162 18.7408L13.2705 18.6865L18.6882 13.2688Z"
            fill="#0C8C5E"
          />
          <path
            d="M21.0104 7.73998V3.87298e-06H13.2704C12.2539 -0.00101453 11.2472 0.198818 10.3081 0.588023C9.36903 0.977229 8.51612 1.54814 7.79835 2.26795L7.74404 2.32226C6.79795 3.28273 6.12105 4.47511 5.78125 5.77977C6.39637 5.62048 7.02851 5.53626 7.66388 5.52893C9.36226 5.50894 11.0196 6.05079 12.3782 7.0702C13.5992 7.98076 14.5233 9.23241 15.0341 10.6674C15.5546 12.134 15.623 13.7231 15.2306 15.2291C16.5355 14.8899 17.728 14.2129 18.6881 13.2663L18.7424 13.2146C19.4625 12.4965 20.0336 11.6432 20.4228 10.7037C20.812 9.76414 21.0117 8.75694 21.0104 7.73998Z"
            fill="#18E299"
          />
        </g>
        <defs>
          <clipPath id="clip0_115_7772">
            <rect width="21" height="21" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
  },
  {
    url: 'https://github.com/scalar/scalar',
    label: 'Scalar',
    github: 'scalar',
    asUser: 'marclave',
    logo: (
      <>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 593 593"
          className="size-9.5"
        >
          <path
            fill="currentColor"
            fillRule="evenodd"
            d="M347 0c6 0 12 5 12 12v134l94-95c5-5 13-5 17 0l72 72c4 4 5 12 0 16v1l-95 94h134c7 0 12 5 12 12v101c0 7-5 12-12 12H447l95 94c4 5 5 13 0 17l-72 72c-4 4-12 5-16 0h-1l-94-95v134c0 7-5 12-12 12H246c-7 0-12-5-12-12v-70c0-22 9-43 24-59l130-130c14-14 14-37 0-51L259 142a84 84 0 0 1-25-59V12c0-7 5-12 12-12zM138 52h1l219 219c14 14 14 37 0 51L139 542c-4 5-12 5-17 0l-71-70c-4-5-5-12 0-17l95-96H12c-7 0-12-5-12-12V246c0-7 5-12 12-12h134l-95-94c-4-5-4-12 0-17l71-71c4-5 12-5 16 0"
          />
        </svg>
        Scalar
      </>
    ),
  },
  {
    url: 'https://launchfa.st',
    label: 'launchfast',
    github: 'launchfast',
    asUser: 'rishi-raj-jain',
    logo: (
      <>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="size-9.5 rounded-full border border-fd-foreground"
          viewBox="0 0 784 784"
        >
          <path
            fill="currentColor"
            mask="url(#launchfast_window)"
            d="M598 186c8.54 10.037 7.44 22.11 6.465 34.497-.24 2.837-.49 5.674-.742 8.51l-.397 4.607c-.346 4.021-.697 8.042-1.05 12.063-.56 6.41-1.116 12.822-1.668 19.233a5334.62 5334.62 0 0 0-.944 10.817l-.176 2.046a362.84 362.84 0 0 0-.75 10.902l-.124 2.334a569.933 569.933 0 0 0-.21 4.423c-.214 4.29-.806 7.778-2.603 11.662-.994 2.365-1.454 4.71-1.989 7.219-1.386 5.796-3.5 11.149-5.812 16.625l-1.044 2.475c-1.747 4.055-3.619 7.83-5.956 11.587-.696 2.191-.696 2.191-1 4h-2l-.715 2.582c-1.437 3.822-3.44 6.853-5.847 10.106l-1.337 1.835c-7.368 10.027-15.5 18.94-24.376 27.64a5694.76 5694.76 0 0 0-18.439 18.188c-3.706 3.67-7.42 7.333-11.147 10.982a1878.074 1878.074 0 0 0-10.793 10.657 633.457 633.457 0 0 1-4.122 4.046 567.68 567.68 0 0 0-5.725 5.67l-1.744 1.668c-1.777 1.731-1.777 1.731-3.755 4.626-.05 3.175-.05 3.175 1 6l.938 3.254c.35 1.092.7 2.184 1.062 3.308 4.092 12.772 3.947 25.655 1.688 38.813l-.357 2.142c-.745 3.913-1.764 6.456-4.331 9.483-.714 2.65-.714 2.65-1 5h-2l-.563 2.438c-1.979 4.905-5.088 8.512-8.437 12.562l-1.5 1.91c-1.116 1.405-2.302 2.755-3.5 4.09h-2v2c-1.8 1.594-1.8 1.594-4.313 3.5A172.533 172.533 0 0 0 455.5 551c-4.207 3.876-8.543 7.476-13.04 11.008-2.548 2.063-4.974 4.22-7.397 6.43A290.988 290.988 0 0 1 419 582l-2.344 1.898c-4.786 3.59-8.404 4.914-14.43 4.485-3.123-.537-4.868-1.233-7.226-3.383-3.406-5.002-5.503-10.483-7.773-16.074-1.287-3.07-2.74-6-4.275-8.95C382 558 382 558 380.742 554.41c-1.931-5.226-4.692-9.921-7.492-14.722l-1.621-2.803c-6.761-12.23-6.761-12.23-15.629-22.885l-1.206 1.253c-1.819 1.882-3.65 3.752-5.481 5.622l-1.897 1.973c-4.408 4.477-7.49 6.89-13.916 7.465-5.833-.521-9.095-3.66-13.082-7.735l-2.576-2.61-2.654-2.718c-5.343-5.45-10.652-10.828-16.503-15.737-1.738-1.56-3.205-3.21-4.685-5.013-2.712-3.293-5.74-5.981-8.984-8.742-3.167-2.761-6.077-5.76-9.016-8.758a1037.58 1037.58 0 0 0-8.883-8.813L262.875 466l-2.168-2.117c-9.094-9.01-9.094-9.01-9.332-15.695.169-5.182 1.954-7.632 5.535-11.235L259 435a1202.76 1202.76 0 0 0 4.438-4.375l2.058-1.96c1.728-1.538 1.728-1.538 1.504-3.665-1.805-1.454-1.805-1.454-4.18-2.906l-2.728-1.74-2.967-1.854-3.026-1.9c-12.059-7.516-23.892-14.714-37.316-19.487-18.847-6.718-18.847-6.718-22.658-12.488-1.59-5.125-2.662-9.655-.09-14.625 1.541-2.13 3.195-4.056 4.965-6l2.086-2.336a866.255 866.255 0 0 1 4.574-5.047c2.276-2.546 4.443-5.15 6.59-7.805 7.3-9.01 14.93-17.75 22.75-26.312 2.398-2.633 4.583-5.228 6.563-8.188 5.777-8.345 15.06-13.848 23.437-19.312l2.016-1.32c17.078-9.77 39.649-8.248 57.984-3.68l3.137 1.164c3.37.984 4.55.977 7.863-.164 2.424-1.87 4.506-4.043 6.632-6.241l1.959-1.977a972.937 972.937 0 0 0 4.197-4.269c2.221-2.273 4.456-4.533 6.694-6.79a6180.08 6180.08 0 0 0 16.656-16.903 4766.2 4766.2 0 0 1 14.043-14.225c2.176-2.201 4.339-4.414 6.501-6.628 42.327-42.95 88.855-50.246 146.518-54.88 2.264-.181 4.527-.364 6.791-.55C585.59 175.325 585.59 175.325 598 186Z"
          />
          <path
            fill="currentColor"
            d="M264.313 503.563c5.121.607 8.929 2.92 12.687 6.437 1.961 5.23 1.59 10.435-.338 15.553-3.284 6.238-8.6 10.938-13.56 15.842-1.08 1.079-2.158 2.159-3.236 3.24-2.251 2.252-4.51 4.498-6.773 6.739a1708.123 1708.123 0 0 0-8.641 8.628c-2.222 2.228-4.45 4.45-6.682 6.668-1.063 1.059-2.124 2.12-3.183 3.183a758.37 758.37 0 0 1-4.471 4.435l-2.561 2.545c-4.848 4.112-9.126 5.743-15.375 5.522-4.382-.714-7.145-3.177-9.993-6.418-1.867-3.046-2.384-5.742-2.5-9.25l-.113-2.238c.981-5.644 5.575-9.857 9.467-13.796l1.48-1.505c1.6-1.624 3.206-3.24 4.815-4.855l3.37-3.397c2.348-2.365 4.7-4.725 7.056-7.083 3.013-3.02 6.01-6.054 9.004-9.093 2.311-2.341 4.634-4.67 6.96-6.997a628.716 628.716 0 0 0 3.317-3.356 520.1 520.1 0 0 1 4.658-4.667l2.668-2.684c3.83-2.935 7.295-2.84 11.944-3.454Z"
          />
          <path
            fill="currentColor"
            d="M233.375 467.125c3.494 2.478 5.295 4.72 6.625 8.875.734 5.765-.781 9.346-4 14-2.25 2.662-4.675 5.12-7.145 7.578l-2.084 2.1c-1.45 1.457-2.903 2.909-4.36 4.357a1034.46 1034.46 0 0 0-6.616 6.668 3703.998 3703.998 0 0 1-4.252 4.25l-1.974 2.008c-5.108 5.031-10.158 8.373-17.569 8.726-3.24-.074-4.603-.37-7.16-2.41-3.744-4.236-5.988-7.316-6.153-13.027l-.113-2.547c1.057-6.71 7.04-11.623 11.64-16.219l2.1-2.112c1.46-1.465 2.92-2.927 4.385-4.387 2.237-2.233 4.46-4.48 6.682-6.727l4.271-4.274 2.005-2.032c6.829-6.761 14.751-9.963 23.718-4.827ZM303.758 541.445c5.337 1.32 9.073 3.911 12.242 8.367 1.871 5.965 1.74 10.17-1 15.688a128.013 128.013 0 0 1-2 3.5h-2v2c-2.375 2.625-2.375 2.625-5 5h-2v2h-2v2c-1.715 1.656-1.715 1.656-3.938 3.5-2.982 2.39-2.982 2.39-5.062 5.5h-2l-.813 1.938C289 593 289 593 286 594v2c-1.645 1.473-1.645 1.473-3.813 3.063l-2.144 1.597c-3.967 2.602-7.401 2.655-12.043 2.34-4.535-1.542-7.49-3.801-10.07-7.781-1.964-4.687-1.955-9.293-.93-14.219 1.84-3.271 4.18-5.55 7-8l1.93-1.691c1.316-1.14 2.64-2.27 3.968-3.395 2.383-2.17 4.475-4.495 6.602-6.914 3.308-3.75 6.692-7.135 10.54-10.328 2.012-1.668 2.012-1.668 3.92-3.656 4.065-4.017 6.948-6.077 12.798-5.57Z"
          />

          <defs>
            <mask id="launchfast_window">
              <rect x="0" y="0" width="784" height="784" fill="white" />
              <circle fill="black" cx="459" cy="323" r="43.84" />
            </mask>
          </defs>
        </svg>
        LaunchFast
      </>
    ),
  },
];
