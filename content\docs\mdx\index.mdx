---
title: Getting Started
description: Introducing Fumadocs MDX, the official content source of Fumadocs.
icon: Album
---

## Quick Start

Get started with Fumadocs MDX:

<Cards>
  <Card title="Next.js" href="/docs/mdx/next">
    Using Fumadocs MDX with Next.js.
  </Card>
  <Card title="React Router" href="/docs/mdx/vite/react-router">
    Using Fumadocs MDX with React Router 7 or above.
  </Card>
  <Card title="Tanstack Start" href="/docs/mdx/vite/tanstack">
    Using Fumadocs MDX with Tanstack Start/Router.
  </Card>
</Cards>

## Introduction

Fumadocs MDX is a tool to transform content into type-safe data, similar to Content Collections.

It is not a full CMS but rather a content compiler + validator, you can use it to handle blog posts and other contents.

### Defining Collections

**Collection** refers to a collection containing a certain type of files. You can define collections in the config file (`source.config.ts`).

Fumadocs MDX transforms collections into type-safe data, accessible in your app. Available collections:

<Tabs items={["doc", "meta", 'docs']}>

    <Tab value='doc'>

Compile Markdown & MDX files into a React Server Component, with useful properties like **Table of Contents**.

```ts title="source.config.ts"
import { defineCollections } from 'fumadocs-mdx/config';

export const test = defineCollections({
  type: 'doc',
  dir: 'content/docs',
});
```

    </Tab>

    <Tab value='meta'>

Transform YAML/JSON files into an array of data.

```ts title="source.config.ts"
import { defineCollections } from 'fumadocs-mdx/config';

export const test = defineCollections({
  type: 'meta',
  dir: 'content/docs',
});
```

    </Tab>
    <Tab value='docs'>

Combination of `meta` and `doc` collections, which is needed for Fumadocs.

```ts title="source.config.ts"
import { defineDocs } from 'fumadocs-mdx/config';

export const docs = defineDocs({
  dir: 'content/docs',
  docs: {
    // options for `doc` collection
  },
  meta: {
    // options for `meta` collection
  },
});
```

    </Tab>

</Tabs>

For example, a `doc` collection will transform the `.md` and `.mdx` files:

<Files>
  <Folder name="folder" defaultOpen>
    <File name="ui.md" />
  </Folder>
  <File name="hello.md" />
  <File name="index.mdx" />
  <File
    name="meta.json"
    className="opacity-50 cursor-not-allowed"
    aria-disabled
  />
</Files>

### Accessing Collections

Collections will be compiled into JavaScript files that your app can access, the output format varies according to the framework you use.

[Get started](#quickstart) with your framework to learn more.

## FAQ

### Built-in Properties

These properties are exported from MDX files by default.

| Property         | Description                                     |
| ---------------- | ----------------------------------------------- |
| `frontmatter`    | Frontmatter                                     |
| `toc`            | Table of Contents                               |
| `structuredData` | Structured Data, useful for implementing search |

### Customise Frontmatter

Use the [`schema`](/docs/mdx/collections#schema-1) option to pass a validation schema to validate frontmatter and define its output properties.

### MDX Plugins

Fumadocs MDX uses [MDX Compiler](https://mdxjs.com/packages/mdx) to compile MDX files into JavaScript files.

You can customise it on [Global Config](/docs/mdx/global#mdx-options) or [Collection Config](/docs/mdx/collections#mdxoptions).
