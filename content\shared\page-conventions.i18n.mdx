You can use different styles for i18n routing.

```ts title="lib/i18n.ts"
import type { I18nConfig } from 'fumadocs-core/i18n';

export const i18n: I18nConfig = {
  // default
  parser: 'dot',
  // or
  parser: 'dir',
};
```

<Tabs items={['dot', 'dir']}>

  <Tab>

    Add Markdown/meta files for different languages by attending `.{locale}` to your file name, like:

    <Files>
      <Folder name="content/docs" defaultOpen>
        <File name="meta.json" />
        <File name="meta.cn.json" />
        <File name="get-started.mdx" />
        <File name="get-started.cn.mdx" />
      </Folder>
    </Files>

    For the default locale, the locale code is optional.

  </Tab>

  <Tab>

    All content files are grouped by language folders:

    <Files>
      <Folder name="content/docs" defaultOpen>
        <Folder name='en' defaultOpen>
          <File name="meta.json" />
          <File name="get-started.mdx" />
        </Folder>

        <Folder name='cn' defaultOpen>
        <File name="meta.json" />
        <File name="get-started.mdx" />
        </Folder>
      </Folder>
    </Files>

  </Tab>

</Tabs>
