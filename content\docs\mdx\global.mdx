---
title: Global Options
description: Customise Fumadocs MDX
---

## Global Options

Shared options of Fumadocs MDX.

```ts title="source.config.ts"
import { defineConfig } from 'fumadocs-mdx/config';

export default defineConfig({
  // global options
});
```

<AutoTypeTable path="./content/docs/mdx/props.ts" name="GlobalConfig" />

### MDX Options

Customise the default MDX processor options, it accepts the [extended MDX options](/docs/mdx/mdx#extended).

```ts title="source.config.ts"
import { defineConfig } from 'fumadocs-mdx/config';
import rehypeKatex from 'rehype-katex';
import remarkMath from 'remark-math';

export default defineConfig({
  mdxOptions: {
    remarkPlugins: [remarkMath],
    // When order matters
    rehypePlugins: (v) => [rehypeKatex, ...v],
  },
});
```

Some default options are applied by Fumadocs MDX, you can also disable them:

```ts title="source.config.ts"
import { defineConfig } from 'fumadocs-mdx/config';

export default defineConfig({
  mdxOptions: {
    preset: 'minimal',
    // now it accepts only MDX processor options
  },
});
```
