{"name": "docs", "version": "0.1.3", "private": true, "scripts": {"build": "pnpm build:pre && next build && pnpm build:post", "build:pre": "bun ./scripts/pre-build.ts", "build:post": "bun ./scripts/post-build.ts", "clean": "rimraf .next", "dev": "next dev --turbo", "lint": "fumadocs-mdx && bun ./scripts/lint.ts && eslint .", "start": "next start"}, "dependencies": {"@ai-sdk/openai-compatible": "^1.0.7", "@ai-sdk/react": "^2.0.15", "@fumadocs/mdx-remote": "workspace:*", "@mixedbread/sdk": "^0.24.0", "@orama/orama": "^3.1.11", "@oramacloud/client": "^2.1.4", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-tooltip": "^1.2.8", "@shikijs/rehype": "^3.9.2", "@shikijs/transformers": "^3.9.2", "ai": "^5.0.15", "class-variance-authority": "^0.7.1", "feed": "^5.1.0", "fumadocs-core": "workspace:*", "fumadocs-docgen": "workspace:^", "fumadocs-mdx": "workspace:*", "fumadocs-openapi": "workspace:^", "fumadocs-twoslash": "workspace:^", "fumadocs-typescript": "workspace:^", "fumadocs-ui": "workspace:*", "hast-util-to-jsx-runtime": "^2.3.6", "katex": "^0.16.22", "lucide-react": "^0.539.0", "mermaid": "^11.9.0", "next": "15.4.6", "next-themes": "^0.4.6", "octokit": "^5.0.3", "oxc-transform": "^0.82.1", "react": "^19.1.1", "react-dom": "^19.1.1", "rehype-katex": "^7.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-mdx": "^3.1.0", "remark-rehype": "^11.1.2", "scroll-into-view-if-needed": "^3.1.0", "shiki": "^3.9.2", "tailwind-merge": "^3.3.1", "twoslash": "^0.3.4", "unist-util-visit": "^5.0.0", "zod": "^4.0.17"}, "devDependencies": {"@fumadocs/cli": "workspace:*", "@next/bundle-analyzer": "15.4.6", "@next/env": "15.4.6", "@next/eslint-plugin-next": "15.4.6", "@tailwindcss/postcss": "^4.1.12", "@types/hast": "^3.0.4", "@types/mdx": "^2.0.13", "@types/node": "24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "algoliasearch": "5.35.0", "eslint-config-custom": "workspace:*", "gray-matter": "^4.0.3", "next-validate-link": "^1.5.2", "postcss": "^8.5.6", "shadcn": "2.10.0", "tailwindcss": "^4.1.12", "tailwindcss-animate": "^1.0.7", "tinyexec": "^1.0.1", "tinyglobby": "^0.2.14", "ts-morph": "^26.0.0", "tsconfig": "workspace:*", "typescript": "^5.9.2"}}