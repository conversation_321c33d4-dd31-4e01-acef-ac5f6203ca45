---
title: Built-in Search
description: Built-in document search of Fumadocs
---

Fumadocs supports document search with Orama, It is the default but also the recommended option since it can be self-hosted and totally free.

## Setup

Host the server for handling search requests.

<Tabs items={['From Source', 'From Search Indexes', 'From Search Indexes (Raw Content)']}>

<Tab>

Create a route handler from source object.

<include cwd meta='title="app/api/search/route.ts"'>
  ../../examples/next-mdx/app/api/search/route.ts
</include>

</Tab>

<Tab>

Pass search indexes to the function, each index needs a `structuredData` field.

Usually, it is provided by your content source (e.g. Fumadocs MDX). You can also extract it from Markdown/MDX document using the [Remark Structure](/docs/headless/mdx/structure) plugin.

<include cwd meta='title="app/api/search/route.ts"'>
  ../../examples/next-mdx/app/api/search/route-full.ts
</include>

</Tab>

<Tab>

Index with the raw content of document (unrecommended).

```ts title="app/api/search/route.ts"
import { allDocs } from 'content-collections';
import { createSearchAPI } from 'fumadocs-core/search/server';

export const { GET } = createSearchAPI('simple', {
  indexes: allDocs.map((docs) => ({
    title: docs.title,
    content: docs.content, // Raw Content
    url: docs.url,
  })),
});
```

</Tab>

</Tabs>

You can search documents using:

- **Fumadocs UI**: Supported out-of-the-box, see [Search UI](/docs/ui/search/orama) for details.
- **Search Client**:

```ts twoslash
import { useDocsSearch } from 'fumadocs-core/search/client';

const client = useDocsSearch({
  type: 'fetch',
});
```

<auto-type-table type='Extract<import("fumadocs-core/search/client").Client, { type: "fetch" }>' />

### Tag Filter

Support filtering results by tag, it's useful for implementing multi-docs similar to this documentation.

<include meta='title="app/api/search/route.ts"' cwd>
  ../../examples/next-mdx/app/api/search/route-tag.ts
</include>

and update your search client:

- **Fumadocs UI**: Configure [Tag Filter](/docs/ui/search/orama#tag-filter) on Search UI.
- **Search Client**: pass a tag to the hook.

```ts
import { useDocsSearch } from 'fumadocs-core/search/client';

const client = useDocsSearch({
  type: 'fetch',
  tag: '<value>', // [!code ++]
});
```

### Static Export

To work with Next.js static export, use `staticGET` from search server.

```ts title="app/api/search/route.ts"
import { source } from '@/lib/source';
import { createFromSource } from 'fumadocs-core/search/server';

// it should be cached forever
export const revalidate = false;

// [!code highlight]
export const { staticGET: GET } = createFromSource(source);
```

> `staticGET` is also available on `createSearchAPI`.

and update your search clients:

- **Fumadocs UI**: use [static client](/docs/ui/search/orama#static) on Search UI.

- **Search Client**: use `static` instead of `fetch`.

  ```ts
  import { useDocsSearch } from 'fumadocs-core/search/client';

  const client = useDocsSearch({
    type: 'static',
  });
  ```

  <AutoTypeTable type='Extract<import("fumadocs-core/search/client").Client, { type: "static" }>' />

<Callout type='warn' title="Be Careful">

    Static Search requires clients to download the exported search indexes.
    For large docs sites, it can be expensive.

    You should use cloud solutions like Orama Cloud or Algolia for these cases.

</Callout>

## Internationalization

Make sure your language is on the Orama [Supported Languages](https://docs.orama.com/docs/orama-js/supported-languages) list.

```ts title="app/api/search/route.ts" tab="createFromSource"
import { source } from '@/lib/source';
import { createFromSource } from 'fumadocs-core/search/server';

export const { GET } = createFromSource(source, {
  localeMap: {
    // [locale]: Orama options
    ru: { language: 'russian' },
    en: { language: 'english' },
  },
});
```

<include cwd meta='title="app/api/search/route.ts" tab="createI18nSearchAPI"'>
  ../../examples/i18n/app/api/search/route-full.ts
</include>

```tsx title="components/search.tsx" tab="Static mode"
import { useDocsSearch } from 'fumadocs-core/search/client';
import { create } from '@orama/orama';

// update your `initOrama` function [!code focus:6]
function initOrama(locale?: string) {
  return create({
    schema: { _: 'string' },
    language: locale === 'ru' ? 'russian' : 'english',
  });
}

function Search() {
  const client = useDocsSearch({
    type: 'static',
    initOrama,
    locale,
    // ...
  });
}
```

For Chinese & Japanese, they require additional configurations:

```npm
@orama/tokenizers
```

<include cwd meta='title="app/api/search/route.ts" tab="createFromSource"'>
  ../../examples/i18n/app/api/search/route.ts
</include>

```tsx tab="Static mode" title="components/search.tsx"
import { useDocsSearch } from 'fumadocs-core/search/client';
import { createTokenizer } from '@orama/tokenizers/mandarin';
import { create } from '@orama/orama';

// [!code focus:8]
function initOrama(locale?: string) {
  return create({
    schema: { _: 'string' },
    components: {
      tokenizer: locale === 'cn' ? createTokenizer() : undefined,
    },
  });
}

function Search() {
  const client = useDocsSearch({
    type: 'static',
    initOrama,
  });
  // ...
}
```

and update your search clients:

- **Fumadocs UI**: No changes needed, Fumadocs UI handles this when you have i18n configured correctly.
- **Search Client**:
  Add `locale` to the search client, this will only allow pages with specified locale to be searchable by the user.

```ts
import { useDocsSearch } from 'fumadocs-core/search/client';

const { search, setSearch, query } = useDocsSearch({
  type: 'fetch',
  locale: 'cn',
});
```

## Headless

You can host the search server on other backend such as Express and Elysia.

```ts
import { initAdvancedSearch } from 'fumadocs-core/search/server';

const server = initAdvancedSearch({
  // you still have to pass indexes
});

server.search('query', {
  // you can specify `locale` and `tag` here
});
```
