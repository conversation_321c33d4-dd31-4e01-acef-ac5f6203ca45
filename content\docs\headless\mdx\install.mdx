---
title: Package Install
description: Generate code blocks for installing packages
---

<Callout type="warn" title='Deprecated'>
    For Fumadocs MDX, it is now enabled by default.

    You can use the `remarkNpm` plugin from `fumadocs-core/mdx-plugins` instead.

</Callout>

## Usage

```package-install
fumadocs-docgen
```

Add the remark plugin.

```ts title="source.config.ts" tab="Fumadocs MDX"
import { remarkInstall } from 'fumadocs-docgen';
import { defineConfig } from 'fumadocs-mdx/config';

export default defineConfig({
  mdxOptions: {
    remarkPlugins: [remarkInstall],
  },
});
```

```ts tab="MDX Compiler"
import { compile } from '@mdx-js/mdx';
import { remarkInstall } from 'fumadocs-docgen';

await compile('...', {
  remarkPlugins: [remarkInstall],
});
```

Define the required components.

```tsx title="mdx-components.tsx (Fumadocs UI)"
import { Tab, Tabs } from 'fumadocs-ui/components/tabs';
import defaultComponents from 'fumadocs-ui/mdx';
import type { MDXComponents } from 'mdx/types';

export function getMDXComponents(components?: MDXComponents): MDXComponents {
  return {
    ...defaultComponents,
    Tab,
    Tabs,
    ...components,
  };
}
```

| Component |                                    |
| --------- | ---------------------------------- |
| Tabs      | Accepts an array of item (`items`) |
| Tab       | Accepts the name of item (`value`) |

Create code blocks with `package-install` as language.

````mdx
```package-install
my-package
```

```package-install
npm i my-package -D
```
````

### Output

The following structure should be generated by the plugin.

```mdx
<Tabs items={['npm', 'pnpm', 'yarn', 'bun']}>
  <Tab value="npm">...</Tab>
  <Tab value="pnpm">...</Tab>
  <Tab value="yarn">...</Tab>
  <Tab value="bun">...</Tab>
</Tabs>
```

```package-install
my-package
```

## Options

### Persistent

When using with Fumadocs UI, you can enable persistence with the `persist` option.

```ts title="source.config.ts" tab="Fumadocs MDX"
import { remarkInstall } from 'fumadocs-docgen';
import { defineConfig } from 'fumadocs-mdx/config';

const remarkInstallOptions = {
  persist: {
    id: 'some-id',
  },
};

export default defineConfig({
  mdxOptions: {
    remarkPlugins: [[remarkInstall, remarkInstallOptions]],
  },
});
```

```ts tab="MDX Compiler"
import { compile } from '@mdx-js/mdx';
import { remarkInstall } from 'fumadocs-docgen';

const remarkInstallOptions = {
  persist: {
    id: 'some-id',
  },
};

await compile('...', {
  remarkPlugins: [[remarkInstall, remarkInstallOptions]],
});
```

This will instead generate:

```mdx
<Tabs groupId="some-id" persist items={[...]}>
  ...
</Tabs>
```
