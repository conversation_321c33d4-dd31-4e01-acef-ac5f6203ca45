---
title: Fumadocs v15.2
description: From Next.js only to Next.js first.
date: 2025-03-28
author: <PERSON><PERSON>
---

## Overview

Fumadocs 15.2 introduces a layer over `fumadocs-core` to extend support to other React frameworks, including React Router and Tanstack Start. Other frameworks can also be supported using the `FrameworkProvider` from `fumadocs-core/framework/base`.

### Why?

The main focus of Fumadocs is flexibility, I aim to support more frameworks as long as it doesn't change the fundamentals of Fumadocs, like the separation of core and UI package.

The React.js ecosystem is a joy to work with, porting Fumadocs to other frameworks isn't as difficult as I thought.
It's also my goal to make it work not only for Next.js devs, but also everyone in the ecosystem who is using a SSR-compatible React framework.

### Breaking Changes

This is a minor release, it shouldn't break any previous usages unless you're relying on the lower level APIs from `fumadocs-core` without `fumadocs-ui`.

Fumadocs Core now requires a `FrameworkProvider`. It's as simple as wrapping it with appropriate provider:

```tsx
import { NextProvider } from 'fumadocs-core/framework/next';

export function Provider({ children }) {
  return <NextProvider>{children}</NextProvider>;
}
```

If you're using Fumadocs UI, there's no need to change. `RootProvider` included it by default, and allows you to provide your own framework with:

```tsx
import { RootProvider } from 'fumadocs-ui/provider/base';

export function Provider({ children }) {
  // now it doesn't add the Next.js provider
  return <RootProvider>{children}</RootProvider>;
}
```

Please report issues if you find any public APIs are broken unexpectedly.

### Compilation Time

15.2 also included some small performance improvements to Fumadocs, the time taken for Turbopack to start dev server and compile its first docs page is about 2-3 seconds.

This change also potentially allows Fumadocs to run on Vite, production build of minimal React Router setup only takes around 4s on a Macbook.

## Try it out

Upgrade from your existing Next.js docs:

```bash
pnpm update -i -r
```

If you want to try the React Router example:

```bash
pnpm create fumadocs-app
```

### Future Plans

15.2 doesn't support Astro, and probably won't unless better React.js support is provided by Astro.

- `transition:persist` is needed for Fumadocs UI layouts, however this will also affect its children (e.g. MDX content), causing page content to be persisted even after navigation.
- React contexts cannot work across islands, this changes the usage of Fumadocs significantly and hence require a redesign of APIs.

Fumadocs will continue to be Next.js first, the docs won't be changed to generalize usages of other frameworks. Instead, a new docs site will be developed for other frameworks (planned).
