---
title: Fumadocs v13
description: Introducing Fumadocs v13
author: <PERSON><PERSON>
date: 2024-07-28
---

## Introduction

Fumadocs v13 has been released. It aims to fix the CSS pollution problem and remove deprecated APIs.

## New Features

### Better Code Blocks

Now supports keeping the original background generated by <PERSON><PERSON> on `CodeBlock` component.

```tsx
import { Pre, CodeBlock } from 'fumadocs-ui/components/codeblock';

<MDX
  components={{
    pre: ({ ref: _ref, ...props }) => (
      <CodeBlock keepBackground {...props}>
        <Pre>{props.children}</Pre>
      </CodeBlock>
    ),
  }}
/>;
```

### Callout

Callout is now a default MDX component, you can use it in MDX files without an import, or manually adding it to MDX components.

```mdx
<Callout type="warn">Hello World</Callout>
```

### New Headless TOC

The headless component of Table of Contents (TOC) now has a separate scroll container provider.

```tsx
import * as Base from 'fumadocs-core/toc';

return (
  <Base.AnchorProvider>
    <Base.ScrollProvider>
      <Base.TOCItem />
      <Base.TOCItem />
    </Base.ScrollProvider>
  </Base.AnchorProvider>
);
```

The anchor provider can be placed anywhere.

### Opt-out of Container

Now supports disabling the default container styles of Tailwind CSS added by Fumadocs UI.

```js
import { createPreset } from 'fumadocs-ui/tailwind-plugin';

/** @type {import('tailwindcss').Config} */
export default {
  presets: [
    createPreset({
      modifyContainer: false,
    }),
  ],
};
```

### Admonition Syntax

In Docusaurus, there's an [Admonition syntax](https://docusaurus.io/docs/markdown-features/admonitions).

For people migrating from Docusaurus, you can enable the new remark plugin to support the Admonition syntax.

See [Remark Admonition](/docs/headless/mdx/remark-admonition).

## Breaking Changes

### Prefix to colors, animations, and utilities

Previously, the Tailwind CSS plugin of Fumadocs UI added colors (including `primary`, `secondary`) which conflicted with Shadcn UI and other design systems.
A `fd-` prefix is added to them to allow the flexibility to have a separated design system on docs.

To use the Fumadocs UI colors on your primary app, enable the `addGlobalColors` option.

```js
import { createPreset } from 'fumadocs-ui/tailwind-plugin';

/** @type {import('tailwindcss').Config} */
export default {
  presets: [
    createPreset({
      addGlobalColors: true,
    }),
  ],
};
```

This adds the colors without the `fd-` prefix.

Or you can just reference them with the `fd-` prefix, like `bg-fd-background`.

### Tailwind CSS Plugin ESM-only

Since Tailwind CSS supports TypeScript configuration, it makes sense to migrate the entire plugin to ESM.

Use ESM syntax in your configuration.

```ts
import { createPreset } from 'fumadocs-ui/tailwind-plugin';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './app/**/*.{ts,tsx}',
    // others
  ],
  presets: [createPreset()],
};
```

### Remove `RollButton` component

`RollButton` was introduced because there were no "Table Of Contents" on mobile viewports.
Now users can use the TOC Popover to switch between headings, it is no longer a suitable design for Fumadocs UI.

You may copy the [last implementation of `RollButton`](https://github.com/fuma-nama/fumadocs/blob/fumadocs-ui%4012.5.6/packages/ui/src/components/roll-button.tsx).

### Better i18n

Now the `I18nProvider` component requires the `locale` prop instead of parsing it from pathname.
This fixed some bugs with the I18n middleware.

We also changed the usage of `translations` to reduce extra translations loaded on the client side.
You have to pass the active translations directly.

`locales` is passed as the available options on the Language Select component.

```tsx
import { RootProvider } from 'fumadocs-ui/provider';
import type { ReactNode } from 'react';
import { I18nProvider } from 'fumadocs-ui/i18n';

export default function Layout({
  params: { lang },
  children,
}: {
  params: { lang: string };
  children: ReactNode;
}) {
  return (
    <html lang={lang}>
      <body>
        <I18nProvider
          locale={lang}
          // options
          locales={[
            {
              name: 'English',
              locale: 'en',
            },
            {
              name: 'Chinese',
              locale: 'cn',
            },
          ]}
          // translations
          translations={
            {
              cn: {
                toc: '目錄',
              },
            }[lang]
          }
        >
          <RootProvider>{children}</RootProvider>
        </I18nProvider>
      </body>
    </html>
  );
}
```

### Code Block Usage

The previous usage was confusing, some props are passed directly to `pre` while some are not.

With v13, pass all props to the `CodeBlock` component.
This also includes class names, you may change your custom styles if necessary.

```tsx
import { Pre, CodeBlock } from 'fumadocs-ui/components/codeblock';

<MDX
  components={{
    // HTML `ref` attribute conflicts with `forwardRef`
    pre: ({ ref: _ref, ...props }) => (
      <CodeBlock {...props}>
        <Pre>{props.children}</Pre>
      </CodeBlock>
    ),
  }}
/>;
```

### Remove Deprecated APIs

- Remove deprecated `fumadocs-ui/components/api` components.
- Replace `secondary` link items with `icon` link items.
- Rename `id` prop on Tabs component to `groupId`.

## Bug Fixes

### UI

- Fixed empty folder animation problems caused by Radix UI.
